import express from "express";
import { chatWithBot } from "../controllers/geminiController.js";
import {
  createChatSession,
  getChatHistory,
  getSessionMessages,
  deleteChatSession,
  clearUserChatHistory,
  getRecentChatSessions
} from "../controllers/chatHistoryController.js";
import authMiddleware from "../middleware/auth.js";

const router = express.Router();

// Chat routes
router.post("/chat", chatWithBot);

// Chat history routes
router.post("/session/create", createChatSession);
router.get("/history/:userId", getChatHistory);
router.get("/session/:sessionId", getSessionMessages);
router.delete("/session/:sessionId", deleteChatSession);
router.delete("/history/:userId", clearUserChatHistory);
router.get("/recent/:userId", getRecentChatSessions);

export default router;
