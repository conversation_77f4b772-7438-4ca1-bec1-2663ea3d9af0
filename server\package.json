{"name": "backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"server": "nodemon server.js", "dev": "nodemon server.js", "start": "node server.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "bcrypt": "^5.1.1", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.510.0", "mongoose": "^8.14.2", "multer": "^1.4.5-lts.2", "nodemon": "^3.1.10", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "stripe": "^18.1.0", "uuid": "^11.1.0", "validator": "^13.15.0"}}