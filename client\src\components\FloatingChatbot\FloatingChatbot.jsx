import React, { useState, useContext } from "react";
import "./FloatingChatbot.css";
import { StoreContext } from "../../context/StoreContext";

const FloatingChatbot = () => {
  const { user, token } = useContext(StoreContext);
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const [chatMode, setChatMode] = useState(null); // 'support' or 'recommendations'
  const [sessionId, setSessionId] = useState(null);
  const [recentSessions, setRecentSessions] = useState([]);

  const toggleChatbot = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      // Load recent sessions when opening
      loadRecentSessions();
      // Reset chat when opening
      setMessages([]);
      setChatMode(null);
      setSessionId(null);
    }
  };

  const loadRecentSessions = async () => {
    if (!user?.id && !user?._id) return;

    try {
      const userId = user?.id || user?._id;
      const response = await fetch(`http://localhost:4000/api/chatbot/recent/${userId}`);
      const data = await response.json();

      if (data.success) {
        setRecentSessions(data.data);
      }
    } catch (error) {
      console.error("Error loading recent sessions:", error);
    }
  };

  const createNewSession = async (mode) => {
    if (!user?.id && !user?._id) {
      // For guest users, just set mode without creating session
      setChatMode(mode);
      const welcomeMessage = mode === 'support'
        ? "Hi! I'm here to help with any questions or issues you might have. How can I assist you today?"
        : "Hello! I'd love to help you discover some delicious food recommendations. What are you in the mood for?";

      setMessages([{ role: "bot", text: welcomeMessage }]);
      return;
    }

    try {
      const userId = user?.id || user?._id;
      const response = await fetch("http://localhost:4000/api/chatbot/session/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId, chatMode: mode }),
      });

      const data = await response.json();

      if (data.success) {
        setSessionId(data.sessionId);
        setChatMode(mode);
        const welcomeMessage = mode === 'support'
          ? "Hi! I'm here to help with any questions or issues you might have. How can I assist you today?"
          : "Hello! I'd love to help you discover some delicious food recommendations. What are you in the mood for?";

        setMessages([{ role: "bot", text: welcomeMessage }]);
      }
    } catch (error) {
      console.error("Error creating session:", error);
      // Fallback to mode without session
      setChatMode(mode);
      const welcomeMessage = mode === 'support'
        ? "Hi! I'm here to help with any questions or issues you might have. How can I assist you today?"
        : "Hello! I'd love to help you discover some delicious food recommendations. What are you in the mood for?";

      setMessages([{ role: "bot", text: welcomeMessage }]);
    }
  };

  const loadSession = async (selectedSessionId) => {
    try {
      const userId = user?.id || user?._id;
      const response = await fetch(`http://localhost:4000/api/chatbot/session/${selectedSessionId}?userId=${userId}`);
      const data = await response.json();

      if (data.success) {
        setSessionId(selectedSessionId);
        setChatMode(data.data.chatMode);
        setMessages(data.data.messages.map(msg => ({
          role: msg.role,
          text: msg.message
        })));
      }
    } catch (error) {
      console.error("Error loading session:", error);
    }
  };

  const selectChatMode = (mode) => {
    createNewSession(mode);
  };

  const sendMessage = async () => {
    if (!input.trim() || !chatMode) return;

    const userMessage = { role: "user", text: input };
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setLoading(true);

    try {
      const response = await fetch("http://localhost:4000/api/chatbot/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: token ? `Bearer ${token}` : "",
        },
        body: JSON.stringify({
          message: input,
          userId: user?.id || user?._id || "guest",
          chatMode: chatMode,
          sessionId: sessionId
        }),
      });

      const data = await response.json();
      const botMessage = { role: "bot", text: data.reply };
      setMessages((prev) => [...prev, botMessage]);
    } catch (err) {
      setMessages((prev) => [
        ...prev,
        { role: "bot", text: "Something went wrong. Please try again." },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") sendMessage();
  };

  const goBack = () => {
    setChatMode(null);
    setMessages([]);
    setSessionId(null);
  };

  return (
    <>
      {/* Floating Chat Button */}
      <div className={`floating-chat-button ${isOpen ? 'open' : ''}`} onClick={toggleChatbot}>
        {isOpen ? (
          <span className="close-icon">✕</span>
        ) : (
          <span className="chat-icon">💬</span>
        )}
      </div>

      {/* Chat Window */}
      {isOpen && (
        <div className="floating-chat-window">
          <div className="floating-chat-header">
            <span>🍽️ Eatzone Assistant</span>
            {chatMode && (
              <button className="back-button" onClick={goBack}>
                ← Back
              </button>
            )}
          </div>

          <div className="floating-chat-content">
            {!chatMode ? (
              // Mode Selection
              <div className="chat-mode-selection">
                <h3>How can I help you today?</h3>

                {/* Recent Sessions */}
                {recentSessions.length > 0 && (
                  <div className="recent-sessions">
                    <p className="recent-sessions-label">Recent Conversations:</p>
                    <div className="recent-sessions-list">
                      {recentSessions.slice(0, 3).map((session) => (
                        <button
                          key={session.sessionId}
                          className="recent-session-btn"
                          onClick={() => loadSession(session.sessionId)}
                        >
                          <span className="session-icon">
                            {session.chatMode === 'support' ? '🎧' : '🍕'}
                          </span>
                          <div className="session-info">
                            <span className="session-type">
                              {session.chatMode === 'support' ? 'Support' : 'Recommendations'}
                            </span>
                            <span className="session-preview">
                              {session.lastMessage?.message?.substring(0, 30)}...
                            </span>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                <div className="mode-buttons">
                  <button
                    className="mode-button support"
                    onClick={() => selectChatMode('support')}
                  >
                    <span className="mode-icon">🎧</span>
                    <div>
                      <strong>Customer Support</strong>
                      <p>Get help with orders, account issues, or general questions</p>
                    </div>
                  </button>
                  <button
                    className="mode-button recommendations"
                    onClick={() => selectChatMode('recommendations')}
                  >
                    <span className="mode-icon">🍕</span>
                    <div>
                      <strong>Food Recommendations</strong>
                      <p>Discover new dishes and get personalized suggestions</p>
                    </div>
                  </button>
                </div>
                <div className="quick-actions">
                  <p className="quick-actions-label">Quick Actions:</p>
                  <div className="quick-action-buttons">
                    <button
                      className="quick-action-btn"
                      onClick={() => {
                        createNewSession('support');
                        setTimeout(() => {
                          setInput("I need help with my recent order");
                        }, 100);
                      }}
                    >
                      📦 Order Help
                    </button>
                    <button
                      className="quick-action-btn"
                      onClick={() => {
                        createNewSession('recommendations');
                        setTimeout(() => {
                          setInput("What's popular today?");
                        }, 100);
                      }}
                    >
                      🔥 Popular Items
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              // Chat Interface
              <>
                <div className="floating-chat-messages">
                  {messages.map((msg, index) => (
                    <div
                      key={index}
                      className={`floating-chat-bubble ${msg.role === "user" ? "user" : "bot"}`}
                    >
                      {msg.text}
                    </div>
                  ))}
                  {loading && <div className="floating-chat-bubble bot">Typing...</div>}
                </div>
                <div className="floating-chat-input">
                  <input
                    type="text"
                    placeholder={`Ask about ${chatMode === 'support' ? 'support' : 'food recommendations'}...`}
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={handleKeyPress}
                  />
                  <button onClick={sendMessage} disabled={!input.trim()}>
                    Send
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default FloatingChatbot;
