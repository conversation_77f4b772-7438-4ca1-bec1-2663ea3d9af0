import { GoogleGenerativeAI } from "@google/generative-ai";
import orderModel from "../models/orderModel.js";
import foodModel from "../models/foodModel.js";
import dotenv from "dotenv";
dotenv.config();

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export const chatWithBot = async (req, res) => {
  const { userId, message, chatMode } = req.body;

  try {
    // Fetch user's recent orders
    const recentOrders = await orderModel.find({ userId }).sort({ date: -1 }).limit(5);
    const orderedFoodIds = recentOrders.flatMap(order => order.items.map(item => item.id));

    const foodItems = await foodModel.find({ _id: { $in: orderedFoodIds } });
    const pastFoodNames = foodItems.map(f => f.name).join(", ");

    // Create different prompts based on chat mode
    let prompt;

    if (chatMode === 'support') {
      prompt = `
        User Message: "${message}"
        Recent Orders: ${pastFoodNames || "No past orders found"}

        You are a helpful customer support assistant for Eatzone, a food delivery app.
        - Help with order issues, account problems, delivery questions, and general inquiries
        - Be empathetic and solution-oriented
        - If you can't solve something, suggest contacting human support
        - Keep responses concise but helpful
        - Use the user's recent orders context when relevant
      `;
    } else if (chatMode === 'recommendations') {
      prompt = `
        User Message: "${message}"
        Recent Orders: ${pastFoodNames || "No past orders found"}

        You are a food recommendation specialist for Eatzone, a food delivery app.
        - Suggest delicious food items based on user preferences and past orders
        - Ask about dietary restrictions, cuisine preferences, or mood
        - Recommend popular items and seasonal specials
        - Be enthusiastic about food and make mouth-watering descriptions
        - Keep recommendations practical and available on food delivery apps
        - Use the user's order history to personalize suggestions
      `;
    } else {
      // Default prompt for backward compatibility
      prompt = `
        User Message: "${message}"
        Recent Orders: ${pastFoodNames || "No past orders found"}

        You are a friendly chatbot for a food delivery app.
        - If the user is asking for food suggestions, recommend based on recent items.
        - If the user is asking about order issues, respond with general helpful info.
        - Always keep your responses short and helpful.
      `;
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
    const result = await model.generateContent(prompt);
    const response = await result.response.text();

    res.json({ reply: response });
  } catch (err) {
    console.error("Chatbot error:", err);
    res.status(500).json({ error: "Failed to communicate with chatbot." });
  }
};
