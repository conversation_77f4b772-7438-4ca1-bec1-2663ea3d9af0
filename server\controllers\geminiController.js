import { GoogleGenerativeAI } from "@google/generative-ai";
import orderModel from "../models/orderModel.js";
import foodModel from "../models/foodModel.js";
import userModel from "../models/userModel.js";
import chatHistoryModel from "../models/chatHistoryModel.js";
import { addMessageToSession } from "./chatHistoryController.js";
import dotenv from "dotenv";
dotenv.config();

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export const chatWithBot = async (req, res) => {
  const { userId, message, chatMode, sessionId } = req.body;

  try {
    // Fetch comprehensive application data
    const [recentOrders, allFoodItems, userInfo] = await Promise.all([
      orderModel.find({ userId }).sort({ date: -1 }).limit(5),
      foodModel.find({}),
      userId !== "guest" ? userModel.findById(userId) : null
    ]);

    // Process order history
    const orderedFoodIds = recentOrders.flatMap(order => order.items.map(item => item.id));
    const pastFoodItems = allFoodItems.filter(item => orderedFoodIds.includes(item._id.toString()));
    const pastFoodNames = pastFoodItems.map(f => f.name).join(", ");

    // Organize food items by category
    const foodByCategory = allFoodItems.reduce((acc, item) => {
      if (!acc[item.category]) acc[item.category] = [];
      acc[item.category].push(item);
      return acc;
    }, {});

    // Application knowledge base
    const appKnowledge = {
      categories: ["Salad", "Rolls", "Deserts", "Sandwich", "Cake", "Pure Veg", "Pasta", "Noodles"],
      features: {
        navigation: ["Home", "Menu", "Mobile-app", "Contact us", "Cart", "Profile", "Orders"],
        userActions: ["Add to cart", "Remove from cart", "Clear cart", "Place order", "Track order", "View profile"],
        authentication: ["Google Sign-in", "Regular login", "Profile management"],
        payment: ["Stripe payment", "INR currency", "Secure checkout", "Order verification"],
        orderStatuses: ["Food processing", "Out for delivery", "Delivered"]
      },
      pricing: {
        currency: "INR (₹)",
        deliveryFee: "₹2",
        paymentMethod: "Stripe (Card payments)"
      }
    };

    // Create comprehensive knowledge base for the chatbot
    const menuItems = allFoodItems.map(item =>
      `${item.name} (${item.category}) - ₹${item.price} - ${item.description}`
    ).join('\n');

    const categoryItems = Object.entries(foodByCategory).map(([category, items]) =>
      `${category}: ${items.map(item => `${item.name} (₹${item.price})`).join(', ')}`
    ).join('\n');

    // Create different prompts based on chat mode
    let prompt;

    if (chatMode === 'support') {
      prompt = `
        You are a helpful customer support assistant for Eatzone, a food delivery app.

        USER MESSAGE: "${message}"
        USER'S RECENT ORDERS: ${pastFoodNames || "No past orders found"}

        EATZONE APP KNOWLEDGE:

        NAVIGATION & FEATURES:
        - Main Pages: Home, Menu, Cart, Profile, Orders, Contact Us
        - User Actions: Add/remove items from cart, place orders, track orders, manage profile
        - Authentication: Google Sign-in and regular login available
        - Payment: Secure Stripe payments in INR (₹) currency
        - Delivery Fee: ₹2 for all orders

        ORDER STATUSES:
        - Food processing → Out for delivery → Delivered

        AVAILABLE FOOD CATEGORIES:
        ${appKnowledge.categories.join(', ')}

        HELP WITH:
        - Order tracking and status updates
        - Account and profile issues
        - Payment and billing questions
        - Delivery information
        - App navigation help
        - Food availability and menu questions

        Be empathetic, solution-oriented, and provide specific help. If you can't solve something, suggest contacting human support.
      `;
    } else if (chatMode === 'recommendations') {
      prompt = `
        You are an enthusiastic food recommendation specialist for Eatzone, a food delivery app.

        USER MESSAGE: "${message}"
        USER'S RECENT ORDERS: ${pastFoodNames || "No past orders found"}

        COMPLETE EATZONE MENU:

        FOOD CATEGORIES & ITEMS:
        ${categoryItems}

        ALL MENU ITEMS:
        ${menuItems}

        RECOMMENDATION GUIDELINES:
        - Suggest specific items from our actual menu with exact names and prices
        - Consider user's past orders for personalized suggestions
        - Ask about dietary preferences, cuisine types, or mood
        - Recommend popular combinations and value meals
        - Mention prices in INR (₹) and describe items appetizingly
        - Suggest items from different categories for variety
        - Consider budget-friendly options and premium choices

        Be enthusiastic about food and make mouth-watering descriptions using our actual menu items!
      `;
    } else {
      // Default prompt with comprehensive app knowledge
      prompt = `
        You are a knowledgeable assistant for Eatzone, a food delivery app.

        USER MESSAGE: "${message}"
        USER'S RECENT ORDERS: ${pastFoodNames || "No past orders found"}

        EATZONE APP COMPLETE KNOWLEDGE:

        MENU CATEGORIES: ${appKnowledge.categories.join(', ')}

        AVAILABLE FOOD ITEMS:
        ${categoryItems}

        APP FEATURES:
        - Navigation: ${appKnowledge.features.navigation.join(', ')}
        - User Actions: ${appKnowledge.features.userActions.join(', ')}
        - Authentication: ${appKnowledge.features.authentication.join(', ')}
        - Payment: ${appKnowledge.features.payment.join(', ')}

        PRICING INFO:
        - Currency: ${appKnowledge.pricing.currency}
        - Delivery Fee: ${appKnowledge.pricing.deliveryFee}
        - Payment: ${appKnowledge.pricing.paymentMethod}

        Help users with food recommendations, app navigation, order questions, or any other inquiries about Eatzone. Be helpful and informative!
      `;
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
    const result = await model.generateContent(prompt);
    const response = await result.response.text();

    // Save chat history if sessionId is provided
    if (sessionId && userId !== "guest") {
      try {
        // Save user message
        await addMessageToSession(userId, sessionId, 'user', message);
        // Save bot response
        await addMessageToSession(userId, sessionId, 'bot', response);
        console.log("Chat history saved for session:", sessionId);
      } catch (historyError) {
        console.error("Error saving chat history:", historyError);
        // Don't fail the request if history saving fails
      }
    }

    res.json({ reply: response });
  } catch (err) {
    console.error("Chatbot error:", err);
    res.status(500).json({ error: "Failed to communicate with chatbot." });
  }
};
